2025-05-24 15:24:41.004 15915-15915 ssioncontroller         pid-15915                            E  Not starting debugger since process cannot load the jdwp agent.
2025-05-24 15:24:42.944 15884-15935 OpenGLRenderer          com.example.aimusicplayer            E  Unable to match the desired swap behavior.
2025-05-24 15:24:43.666   576-1633  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-24 15:24:43.666   576-1633  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-24 15:24:43.666   576-1633  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-24 15:24:43.761   576-1924  CellBroadcastUtils      system_server                        E  getDefaultCellBroadcastReceiverPackageName: no package found
---------------------------- PROCESS ENDED (15884) for package com.example.aimusicplayer ----------------------------
2025-05-24 15:24:45.771 15884-15935 OpenGLRenderer          com.example.aimusicplayer            E  Unable to match the desired swap behavior.
2025-05-24 15:24:45.775 15884-15884 AndroidRuntime          com.example.aimusicplayer            E  FATAL EXCEPTION: main (Ask Gemini)
                                                                                                    Process: com.example.aimusicplayer, PID: 15884
                                                                                                    java.lang.NullPointerException: needleBitmap must not be null
                                                                                                    	at com.example.aimusicplayer.ui.widget.AlbumCoverView.initSize(AlbumCoverView.kt:114)
                                                                                                    	at com.example.aimusicplayer.ui.widget.AlbumCoverView.onSizeChanged(AlbumCoverView.kt:107)
                                                                                                    	at android.view.View.sizeChange(View.java:23989)
                                                                                                    	at android.view.View.setFrame(View.java:23941)
                                                                                                    	at android.view.View.layout(View.java:23792)
                                                                                                    	at android.widget.RelativeLayout.onLayout(RelativeLayout.java:1103)
                                                                                                    	at android.view.View.layout(View.java:23798)
                                                                                                    	at android.view.ViewGroup.layout(ViewGroup.java:6413)
                                                                                                    	at android.widget.LinearLayout.setChildFrame(LinearLayout.java:1891)
                                                                                                    	at android.widget.LinearLayout.layoutHorizontal(LinearLayout.java:1880)
                                                                                                    	at android.widget.LinearLayout.onLayout(LinearLayout.java:1640)
                                                                                                    	at android.view.View.layout(View.java:23798)
                                                                                                    	at android.view.ViewGroup.layout(ViewGroup.java:6413)
                                                                                                    	at android.widget.RelativeLayout.onLayout(RelativeLayout.java:1103)
                                                                                                    	at android.view.View.layout(View.java:23798)
                                                                                                    	at android.view.ViewGroup.layout(ViewGroup.java:6413)
                                                                                                    	at android.widget.FrameLayout.layoutChildren(FrameLayout.java:332)
                                                                                                    	at android.widget.FrameLayout.onLayout(FrameLayout.java:270)
                                                                                                    	at android.view.View.layout(View.java:23798)
                                                                                                    	at android.view.ViewGroup.layout(ViewGroup.java:6413)
                                                                                                    	at android.widget.FrameLayout.layoutChildren(FrameLayout.java:332)
                                                                                                    	at android.widget.FrameLayout.onLayout(FrameLayout.java:270)
                                                                                                    	at android.view.View.layout(View.java:23798)
                                                                                                    	at android.view.ViewGroup.layout(ViewGroup.java:6413)
                                                                                                    	at android.widget.RelativeLayout.onLayout(RelativeLayout.java:1103)
                                                                                                    	at android.view.View.layout(View.java:23798)
                                                                                                    	at android.view.ViewGroup.layout(ViewGroup.java:6413)
                                                                                                    	at android.widget.FrameLayout.layoutChildren(FrameLayout.java:332)
                                                                                                    	at android.widget.FrameLayout.onLayout(FrameLayout.java:270)
                                                                                                    	at android.view.View.layout(View.java:23798)
                                                                                                    	at android.view.ViewGroup.layout(ViewGroup.java:6413)
                                                                                                    	at android.widget.LinearLayout.setChildFrame(LinearLayout.java:1891)
                                                                                                    	at android.widget.LinearLayout.layoutVertical(LinearLayout.java:1729)
                                                                                                    	at android.widget.LinearLayout.onLayout(LinearLayout.java:1638)
                                                                                                    	at android.view.View.layout(View.java:23798)
                                                                                                    	at android.view.ViewGroup.layout(ViewGroup.java:6413)
                                                                                                    	at android.widget.FrameLayout.layoutChildren(FrameLayout.java:332)
                                                                                                    	at android.widget.FrameLayout.onLayout(FrameLayout.java:270)
                                                                                                    	at android.view.View.layout(View.java:23798)
                                                                                                    	at android.view.ViewGroup.layout(ViewGroup.java:6413)
                                                                                                    	at android.widget.LinearLayout.setChildFrame(LinearLayout.java:1891)
                                                                                                    	at android.widget.LinearLayout.layoutVertical(LinearLayout.java:1729)
                                                                                                    	at android.widget.LinearLayout.onLayout(LinearLayout.java:1638)
                                                                                                    	at android.view.View.layout(View.java:23798)
                                                                                                    	at android.view.ViewGroup.layout(ViewGroup.java:6413)
                                                                                                    	at android.widget.FrameLayout.layoutChildren(FrameLayout.java:332)
                                                                                                    	at android.widget.FrameLayout.onLayout(FrameLayout.java:270)
                                                                                                    	at com.android.internal.policy.DecorView.onLayout(DecorView.java:799)
                                                                                                    	at android.view.View.layout(View.java:23798)
                                                                                                    	at android.view.ViewGroup.layout(ViewGroup.java:6413)
                                                                                                    	at android.view.ViewRootImpl.performLayout(ViewRootImpl.java:4118)
                                                                                                    	at android.view.ViewRootImpl.performTraversals(ViewRootImpl.java:3459)
                                                                                                    	at android.view.ViewRootImpl.doTraversal(ViewRootImpl.java:2371)
                                                                                                    	at android.view.ViewRootImpl$TraversalRunnable.run(ViewRootImpl.java:9297)
                                                                                                    	at android.view.Choreographer$CallbackRecord.run(Choreographer.java:1231)
                                                                                                    	at android.view.Choreographer$CallbackRecord.run(Choreographer.java:1239)
                                                                                                    	at android.view.Choreographer.doCallbacks(Choreographer.java:899)
                                                                                                    	at android.view.Choreographer.doFrame(Choreographer.java:832)
                                                                                                    	at android.view.Choreographer$FrameDisplayEventReceiver.run(Choreographer.java:1214)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:942)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:99)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:201)
                                                                                                    	at android.os.Looper.loop(Looper.java:288)
2025-05-24 15:24:45.776 15884-15884 AndroidRuntime          com.example.aimusicplayer            E  	at android.app.ActivityThread.main(ActivityThread.java:7924) (Ask Gemini)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)
2025-05-24 15:24:45.963   576-1979  InputDispatcher         system_server                        E  But another display has a focused window
                                                                                                      FocusedWindows:
                                                                                                        displayId=8, name='ff3e4b3 com.android.systemui/com.android.systemui.car.distantdisplay.activity.RootTaskViewWallpaperActivity'
                                                                                                        displayId=3, name='678a1bd com.android.systemui/com.android.systemui.car.distantdisplay.activity.DistantDisplayActivity'
                                                                                                        displayId=7, name='7a010fa com.android.systemui/com.android.systemui.car.distantdisplay.activity.NavigationTaskViewWallpaperActivity'
                                                                                                        displayId=2, name='2d2ce0d com.android.car.cluster.osdouble/com.android.car.cluster.osdouble.ClusterOsDoubleActivity'
2025-05-24 15:24:46.079  4386-4461  OpenGLRenderer          com.android.car.carlauncher          E  Unable to match the desired swap behavior.
2025-05-24 15:24:46.126   362-362   ClientCache             surfaceflinger                       E  failed to get buffer, invalid process token
2025-05-24 15:24:46.128   362-362   BpTransact...edListener surfaceflinger                       E  Failed to transact (-32)
2025-05-24 15:24:47.788   576-1633  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-24 15:24:47.788   576-1633  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-24 15:24:47.788   576-1633  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
                                                                                                    	at acei.b(PG:356)
                                                                                                    	at acdu.run(PG:233)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
2025-05-24 15:21:32.958   576-704   WifiScoringParams       system_server                        E  Invalid frequency(-1), using 5G as default rssi array
2025-05-24 15:22:05.203 14175-14205 Finsky                  com.android.vending                  E  [320] okc.i(3): Failed to registerSync with Phenotype for experiment package com.google.android.finsky.stable. (Ask Gemini)
                                                                                                    java.util.concurrent.ExecutionException: com.google.android.libraries.phenotype.client.api.PhenotypeRuntimeException: 29504: 29504: Network error
                                                                                                    	at abkz.r(PG:21)
                                                                                                    	at abkz.get(PG:10)
                                                                                                    	at okc.h(PG:39)
                                                                                                    	at okc.doInBackground(PG:42)
                                                                                                    	at oke.d(PG:4)
                                                                                                    	at man.call(PG:997)
                                                                                                    	at abnn.a(PG:3)
                                                                                                    	at abmr.run(PG:21)
                                                                                                    	at abno.run(PG:5)
                                                                                                    	at abng.run(PG:76)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at dvo.run(PG:38)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
                                                                                                    Caused by: com.google.android.libraries.phenotype.client.api.PhenotypeRuntimeException: 29504: 29504: Network error
                                                                                                    	at xvr.call(PG:475)
                                                                                                    	at abnn.a(PG:3)
                                                                                                    	at abmr.run(PG:21)
                                                                                                    	at abno.run(PG:5)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637) 
                                                                                                    	at dvo.run(PG:38) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
                                                                                                    Caused by: com.google.android.gms.phenotype.core.common.PhenotypeRuntimeException: 29504: Network error
                                                                                                    	at vsy.D(PG:230)
                                                                                                    	at vsy.n(PG:108)
                                                                                                    	at xvr.call(PG:386)
                                                                                                    	at abnn.a(PG:3) 
                                                                                                    	at abmr.run(PG:21) 
                                                                                                    	at abno.run(PG:5) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637) 
                                                                                                    	at dvo.run(PG:38) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
                                                                                                    Caused by: org.apache.http.conn.ConnectTimeoutException: Connect to /142.250.69.170:443 timed out
                                                                                                    	at org.apache.http.conn.scheme.PlainSocketFactory.connectSocket(PlainSocketFactory.java:126)
                                                                                                    	at org.apache.http.impl.conn.DefaultClientConnectionOperator.openConnection(DefaultClientConnectionOperator.java:149)
                                                                                                    	at org.apache.http.impl.conn.AbstractPoolEntry.open(AbstractPoolEntry.java:170)
                                                                                                    	at org.apache.http.impl.conn.AbstractPooledConnAdapter.open(AbstractPooledConnAdapter.java:124)
                                                                                                    	at org.apache.http.impl.client.DefaultRequestDirector.execute(DefaultRequestDirector.java:366)
                                                                                                    	at org.apache.http.impl.client.AbstractHttpClient.execute(AbstractHttpClient.java:560)
                                                                                                    	at org.apache.http.impl.client.AbstractHttpClient.execute(AbstractHttpClient.java:492)
                                                                                                    	at org.apache.http.impl.client.AbstractHttpClient.execute(AbstractHttpClient.java:470)
                                                                                                    	at zba.execute(PG:5)
                                                                                                    	at abda.d(PG:7)
                                                                                                    	at zbe.b(PG:82)
                                                                                                    	at zbe.execute(PG:23)
                                                                                                    	at zbe.execute(PG:13)
                                                                                                    	at vtc.a(PG:230)
                                                                                                    	at vtw.g(PG:1)
                                                                                                    	at vtc.b(PG:1)
                                                                                                    	at vsy.D(PG:18)
                                                                                                    	at vsy.n(PG:108) 
                                                                                                    	at xvr.call(PG:386) 
                                                                                                    	at abnn.a(PG:3) 
                                                                                                    	at abmr.run(PG:21) 
                                                                                                    	at abno.run(PG:5) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637) 
                                                                                                    	at dvo.run(PG:38) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
2025-05-24 15:22:05.221 14175-14206 Finsky                  com.android.vending                  E  [321] iuw.a(52): Unexpected android-id = 0
2025-05-24 15:22:05.244 14175-14206 Finsky                  com.android.vending                  E  [321] jhs.c(122): Unable to fetch checkin consistency token: empty token
2025-05-24 15:22:06.109 14257-14310 Finsky                  com.android.vending                  E  [318] okc.i(3): Failed to registerSync with Phenotype for experiment package com.google.android.finsky.stable. (Ask Gemini)
                                                                                                    java.util.concurrent.ExecutionException: com.google.android.libraries.phenotype.client.api.PhenotypeRuntimeException: 29504: 29504: Network error
                                                                                                    	at abkz.r(PG:21)
                                                                                                    	at abkz.get(PG:10)
                                                                                                    	at okc.h(PG:39)
                                                                                                    	at okc.doInBackground(PG:42)
                                                                                                    	at oke.d(PG:4)
                                                                                                    	at man.call(PG:997)
                                                                                                    	at abnn.a(PG:3)
                                                                                                    	at abmr.run(PG:21)
                                                                                                    	at abno.run(PG:5)
                                                                                                    	at abng.run(PG:76)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at dvo.run(PG:38)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
                                                                                                    Caused by: com.google.android.libraries.phenotype.client.api.PhenotypeRuntimeException: 29504: 29504: Network error
                                                                                                    	at xvr.call(PG:475)
                                                                                                    	at abnn.a(PG:3)
                                                                                                    	at abmr.run(PG:21)
                                                                                                    	at abno.run(PG:5)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637) 
                                                                                                    	at dvo.run(PG:38) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
                                                                                                    Caused by: com.google.android.gms.phenotype.core.common.PhenotypeRuntimeException: 29504: Network error
                                                                                                    	at vsy.D(PG:230)
                                                                                                    	at vsy.n(PG:108)
                                                                                                    	at xvr.call(PG:386)
                                                                                                    	at abnn.a(PG:3) 
                                                                                                    	at abmr.run(PG:21) 
                                                                                                    	at abno.run(PG:5) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637) 
                                                                                                    	at dvo.run(PG:38) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
                                                                                                    Caused by: org.apache.http.conn.ConnectTimeoutException: Connect to /142.250.69.170:443 timed out
                                                                                                    	at org.apache.http.conn.scheme.PlainSocketFactory.connectSocket(PlainSocketFactory.java:126)
                                                                                                    	at org.apache.http.impl.conn.DefaultClientConnectionOperator.openConnection(DefaultClientConnectionOperator.java:149)
                                                                                                    	at org.apache.http.impl.conn.AbstractPoolEntry.open(AbstractPoolEntry.java:170)
                                                                                                    	at org.apache.http.impl.conn.AbstractPooledConnAdapter.open(AbstractPooledConnAdapter.java:124)
                                                                                                    	at org.apache.http.impl.client.DefaultRequestDirector.execute(DefaultRequestDirector.java:366)
                                                                                                    	at org.apache.http.impl.client.AbstractHttpClient.execute(AbstractHttpClient.java:560)
                                                                                                    	at org.apache.http.impl.client.AbstractHttpClient.execute(AbstractHttpClient.java:492)
                                                                                                    	at org.apache.http.impl.client.AbstractHttpClient.execute(AbstractHttpClient.java:470)
                                                                                                    	at zba.execute(PG:5)
                                                                                                    	at abda.d(PG:7)
                                                                                                    	at zbe.b(PG:82)
                                                                                                    	at zbe.execute(PG:23)
                                                                                                    	at zbe.execute(PG:13)
                                                                                                    	at vtc.a(PG:230)
                                                                                                    	at vtw.g(PG:1)
                                                                                                    	at vtc.b(PG:1)
                                                                                                    	at vsy.D(PG:18)
                                                                                                    	at vsy.n(PG:108) 
                                                                                                    	at xvr.call(PG:386) 
                                                                                                    	at abnn.a(PG:3) 
                                                                                                    	at abmr.run(PG:21) 
                                                                                                    	at abno.run(PG:5) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637) 
                                                                                                    	at dvo.run(PG:38) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
2025-05-24 15:22:06.136 14257-14348 Finsky                  com.android.vending                  E  [335] iuw.a(52): Unexpected android-id = 0
2025-05-24 15:22:06.164 14257-14348 Finsky                  com.android.vending                  E  [335] jhs.c(122): Unable to fetch checkin consistency token: empty token
2025-05-24 15:22:23.272  2140-2558  RadioStationSyncImpl    com.google.android.carassistant      E  Error retrieving the OEM radio App's browse tree (Ask Gemini)
                                                                                                    java.lang.IllegalStateException: resolveInfo is null
                                                                                                    	at izy.c(PG:28)
                                                                                                    	at jdj.a(PG:35)
                                                                                                    	at jcz.b(PG:42)
                                                                                                    	at snx.a(PG:145)
                                                                                                    	at uzf.a(PG:105)
                                                                                                    	at uzf.a(PG:105)
                                                                                                    	at yap.a(PG:3)
                                                                                                    	at xzv.run(PG:19)
                                                                                                    	at yar.run(PG:5)
                                                                                                    	at xyq.execute(PG:1)
                                                                                                    	at xos.x(PG:6)
                                                                                                    	at wab.bi(PG:5)
                                                                                                    	at vbt.b(PG:70)
                                                                                                    	at adkk.k(PG:51)
                                                                                                    	at ugo.b(PG:21)
                                                                                                    	at com.google.apps.tiktok.contrib.work.TikTokListenableWorker.b(PG:85)
                                                                                                    	at crj.eF(PG:115)
                                                                                                    	at acvf.fW(PG:12)
                                                                                                    	at aday.run(PG:109)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:942)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:99)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:201)
                                                                                                    	at android.os.Looper.loop(Looper.java:288)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:7924)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)
2025-05-24 15:22:31.836   346-346   android.ha...rvice-mock <EMAIL>  E  Failed to getEnergyData
2025-05-24 15:23:23.120 14175-15443 FirebaseInstanceId      com.android.vending                  E  Failed to get FIS auth token (Ask Gemini)
                                                                                                    java.util.concurrent.ExecutionException: com.google.firebase.installations.FirebaseInstallationsException: Firebase Installations Service is unavailable. Please try again later.
                                                                                                    	at vtw.iJ(PG:32)
                                                                                                    	at vtw.G(PG:31)
                                                                                                    	at afgv.i(PG:183)
                                                                                                    	at acdh.a(PG:191)
                                                                                                    	at ujn.run(PG:251)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at vbl.run(PG:7)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
                                                                                                    Caused by: com.google.firebase.installations.FirebaseInstallationsException: Firebase Installations Service is unavailable. Please try again later.
                                                                                                    	at acei.b(PG:356)
                                                                                                    	at acdu.run(PG:233)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
2025-05-24 15:23:24.428 14257-15454 FirebaseInstanceId      com.android.vending                  E  Failed to get FIS auth token (Ask Gemini)
                                                                                                    java.util.concurrent.ExecutionException: com.google.firebase.installations.FirebaseInstallationsException: Firebase Installations Service is unavailable. Please try again later.
                                                                                                    	at vtw.iJ(PG:32)
                                                                                                    	at vtw.G(PG:31)
                                                                                                    	at afgv.i(PG:183)
                                                                                                    	at acdh.a(PG:191)
                                                                                                    	at ujn.run(PG:251)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at vbl.run(PG:7)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
                                                                                                    Caused by: com.google.firebase.installations.FirebaseInstallationsException: Firebase Installations Service is unavailable. Please try again later.
                                                                                                    	at acei.b(PG:356)
                                                                                                    	at acdu.run(PG:233)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
2025-05-24 15:24:12.995   576-704   WifiScoringParams       system_server                        E  Invalid frequency(-1), using 5G as default rssi array
2025-05-24 15:24:25.928 14175-14205 Finsky                  com.android.vending                  E  [320] okc.i(3): Failed to registerSync with Phenotype for experiment package com.google.android.finsky.stable. (Ask Gemini)
                                                                                                    java.util.concurrent.ExecutionException: com.google.android.libraries.phenotype.client.api.PhenotypeRuntimeException: 29504: 29504: Network error
                                                                                                    	at abkz.r(PG:21)
                                                                                                    	at abkz.get(PG:10)
                                                                                                    	at okc.h(PG:39)
                                                                                                    	at okc.doInBackground(PG:42)
                                                                                                    	at oke.d(PG:4)
                                                                                                    	at man.call(PG:997)
                                                                                                    	at abnn.a(PG:3)
                                                                                                    	at abmr.run(PG:21)
                                                                                                    	at abno.run(PG:5)
                                                                                                    	at abng.run(PG:76)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at dvo.run(PG:38)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
                                                                                                    Caused by: com.google.android.libraries.phenotype.client.api.PhenotypeRuntimeException: 29504: 29504: Network error
                                                                                                    	at xvr.call(PG:475)
                                                                                                    	at abnn.a(PG:3)
                                                                                                    	at abmr.run(PG:21)
                                                                                                    	at abno.run(PG:5)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637) 
                                                                                                    	at dvo.run(PG:38) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
                                                                                                    Caused by: com.google.android.gms.phenotype.core.common.PhenotypeRuntimeException: 29504: Network error
                                                                                                    	at vsy.D(PG:230)
                                                                                                    	at vsy.n(PG:108)
                                                                                                    	at xvr.call(PG:386)
                                                                                                    	at abnn.a(PG:3) 
                                                                                                    	at abmr.run(PG:21) 
                                                                                                    	at abno.run(PG:5) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637) 
                                                                                                    	at dvo.run(PG:38) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
                                                                                                    Caused by: org.apache.http.conn.ConnectTimeoutException: Connect to /172.217.14.202:443 timed out
                                                                                                    	at org.apache.http.conn.scheme.PlainSocketFactory.connectSocket(PlainSocketFactory.java:126)
                                                                                                    	at org.apache.http.impl.conn.DefaultClientConnectionOperator.openConnection(DefaultClientConnectionOperator.java:149)
                                                                                                    	at org.apache.http.impl.conn.AbstractPoolEntry.open(AbstractPoolEntry.java:170)
                                                                                                    	at org.apache.http.impl.conn.AbstractPooledConnAdapter.open(AbstractPooledConnAdapter.java:124)
                                                                                                    	at org.apache.http.impl.client.DefaultRequestDirector.execute(DefaultRequestDirector.java:366)
                                                                                                    	at org.apache.http.impl.client.AbstractHttpClient.execute(AbstractHttpClient.java:560)
                                                                                                    	at org.apache.http.impl.client.AbstractHttpClient.execute(AbstractHttpClient.java:492)
                                                                                                    	at org.apache.http.impl.client.AbstractHttpClient.execute(AbstractHttpClient.java:470)
                                                                                                    	at zba.execute(PG:5)
                                                                                                    	at abda.d(PG:7)
                                                                                                    	at zbe.b(PG:82)
                                                                                                    	at zbe.execute(PG:23)
                                                                                                    	at zbe.execute(PG:13)
                                                                                                    	at vtc.a(PG:230)
                                                                                                    	at vtw.g(PG:1)
                                                                                                    	at vtc.b(PG:1)
                                                                                                    	at vsy.D(PG:18)
                                                                                                    	at vsy.n(PG:108) 
                                                                                                    	at xvr.call(PG:386) 
                                                                                                    	at abnn.a(PG:3) 
                                                                                                    	at abmr.run(PG:21) 
                                                                                                    	at abno.run(PG:5) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637) 
                                                                                                    	at dvo.run(PG:38) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
2025-05-24 15:24:26.027 14175-14206 Finsky                  com.android.vending                  E  [321] iuw.a(52): Unexpected android-id = 0
2025-05-24 15:24:26.069 14175-14206 Finsky                  com.android.vending                  E  [321] jhs.c(122): Unable to fetch checkin consistency token: empty token
2025-05-24 15:24:26.890 14257-14310 Finsky                  com.android.vending                  E  [318] okc.i(3): Failed to registerSync with Phenotype for experiment package com.google.android.finsky.stable. (Ask Gemini)
                                                                                                    java.util.concurrent.ExecutionException: com.google.android.libraries.phenotype.client.api.PhenotypeRuntimeException: 29504: 29504: Network error
                                                                                                    	at abkz.r(PG:21)
                                                                                                    	at abkz.get(PG:10)
                                                                                                    	at okc.h(PG:39)
                                                                                                    	at okc.doInBackground(PG:42)
                                                                                                    	at oke.d(PG:4)
                                                                                                    	at man.call(PG:997)
                                                                                                    	at abnn.a(PG:3)
                                                                                                    	at abmr.run(PG:21)
                                                                                                    	at abno.run(PG:5)
                                                                                                    	at abng.run(PG:76)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at dvo.run(PG:38)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
                                                                                                    Caused by: com.google.android.libraries.phenotype.client.api.PhenotypeRuntimeException: 29504: 29504: Network error
                                                                                                    	at xvr.call(PG:475)
                                                                                                    	at abnn.a(PG:3)
                                                                                                    	at abmr.run(PG:21)
                                                                                                    	at abno.run(PG:5)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637) 
                                                                                                    	at dvo.run(PG:38) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
                                                                                                    Caused by: com.google.android.gms.phenotype.core.common.PhenotypeRuntimeException: 29504: Network error
                                                                                                    	at vsy.D(PG:230)
                                                                                                    	at vsy.n(PG:108)
                                                                                                    	at xvr.call(PG:386)
                                                                                                    	at abnn.a(PG:3) 
                                                                                                    	at abmr.run(PG:21) 
                                                                                                    	at abno.run(PG:5) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637) 
                                                                                                    	at dvo.run(PG:38) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
                                                                                                    Caused by: org.apache.http.conn.ConnectTimeoutException: Connect to /172.217.14.202:443 timed out
                                                                                                    	at org.apache.http.conn.scheme.PlainSocketFactory.connectSocket(PlainSocketFactory.java:126)
                                                                                                    	at org.apache.http.impl.conn.DefaultClientConnectionOperator.openConnection(DefaultClientConnectionOperator.java:149)
                                                                                                    	at org.apache.http.impl.conn.AbstractPoolEntry.open(AbstractPoolEntry.java:170)
                                                                                                    	at org.apache.http.impl.conn.AbstractPooledConnAdapter.open(AbstractPooledConnAdapter.java:124)
                                                                                                    	at org.apache.http.impl.client.DefaultRequestDirector.execute(DefaultRequestDirector.java:366)
                                                                                                    	at org.apache.http.impl.client.AbstractHttpClient.execute(AbstractHttpClient.java:560)
                                                                                                    	at org.apache.http.impl.client.AbstractHttpClient.execute(AbstractHttpClient.java:492)
                                                                                                    	at org.apache.http.impl.client.AbstractHttpClient.execute(AbstractHttpClient.java:470)
                                                                                                    	at zba.execute(PG:5)
                                                                                                    	at abda.d(PG:7)
                                                                                                    	at zbe.b(PG:82)
                                                                                                    	at zbe.execute(PG:23)
                                                                                                    	at zbe.execute(PG:13)
                                                                                                    	at vtc.a(PG:230)
                                                                                                    	at vtw.g(PG:1)
                                                                                                    	at vtc.b(PG:1)
                                                                                                    	at vsy.D(PG:18)
                                                                                                    	at vsy.n(PG:108) 
                                                                                                    	at xvr.call(PG:386) 
                                                                                                    	at abnn.a(PG:3) 
                                                                                                    	at abmr.run(PG:21) 
                                                                                                    	at abno.run(PG:5) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637) 
                                                                                                    	at dvo.run(PG:38) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
2025-05-24 15:24:26.910 14257-15755 Finsky                  com.android.vending                  E  [419] iuw.a(52): Unexpected android-id = 0
2025-05-24 15:24:26.945 14257-15755 Finsky                  com.android.vending                  E  [419] jhs.c(122): Unable to fetch checkin consistency token: empty token
2025-05-24 15:24:31.834   346-346   android.ha...rvice-mock <EMAIL>  E  Failed to getEnergyData
2025-05-24 15:24:38.835   952-952   SmsApplication          com.android.phone                    E  com.android.mms.service does not have system signature
2025-05-24 15:24:38.940   446-453   installd                installd                             E  Couldn't opendir /data/app/vmdl1719337291.tmp: No such file or directory
2025-05-24 15:24:38.940   446-453   installd                installd                             E  Failed to delete /data/app/vmdl1719337291.tmp: No such file or directory
2025-05-24 15:24:39.036   952-952   SmsApplication          com.android.phone                    E  com.android.mms.service does not have system signature
2025-05-24 15:24:39.075   952-952   SmsApplication          com.android.phone                    E  com.android.mms.service does not have system signature
2025-05-24 15:24:39.428 15835-15835 ndroid.keychain         com.android.keychain                 E  Not starting debugger since process cannot load the jdwp agent.
2025-05-24 15:24:39.527  2013-2013  ChimeraRcvrProxy        com.google.android.gms               E  com.google.android.gms.gass.chimera.PackageChangeBroadcastReceiver dropping broadcast android.intent.action.PACKAGE_ADDED
2025-05-24 15:24:39.720  2013-2013  ChimeraRcvrProxy        com.google.android.gms               E  com.google.android.gms.gass.chimera.PackageChangeBroadcastReceiver dropping broadcast android.intent.action.PACKAGE_REPLACED
2025-05-24 15:24:39.956   576-768   CellBroadcastUtils      system_server                        E  getDefaultCellBroadcastReceiverPackageName: no package found
2025-05-24 15:24:40.299  4386-4461  OpenGLRenderer          com.android.car.carlauncher          E  Unable to match the desired swap behavior.
2025-05-24 15:24:41.004 15915-15915 ssioncontroller         com....android.permissioncontroller  E  Not starting debugger since process cannot load the jdwp agent.
2025-05-24 15:24:42.944 15884-15935 OpenGLRenderer          com.example.aimusicplayer            E  Unable to match the desired swap behavior.
2025-05-24 15:24:43.666   576-1633  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-24 15:24:43.666   576-1633  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-24 15:24:43.666   576-1633  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-24 15:24:43.761   576-1924  CellBroadcastUtils      system_server                        E  getDefaultCellBroadcastReceiverPackageName: no package found
2025-05-24 15:24:45.771 15884-15935 OpenGLRenderer          com.example.aimusicplayer            E  Unable to match the desired swap behavior.
2025-05-24 15:24:45.775 15884-15884 AndroidRuntime          com.example.aimusicplayer            E  FATAL EXCEPTION: main (Ask Gemini)
                                                                                                    Process: com.example.aimusicplayer, PID: 15884
                                                                                                    java.lang.NullPointerException: needleBitmap must not be null
                                                                                                    	at com.example.aimusicplayer.ui.widget.AlbumCoverView.initSize(AlbumCoverView.kt:114)
                                                                                                    	at com.example.aimusicplayer.ui.widget.AlbumCoverView.onSizeChanged(AlbumCoverView.kt:107)
                                                                                                    	at android.view.View.sizeChange(View.java:23989)
                                                                                                    	at android.view.View.setFrame(View.java:23941)
                                                                                                    	at android.view.View.layout(View.java:23792)
                                                                                                    	at android.widget.RelativeLayout.onLayout(RelativeLayout.java:1103)
                                                                                                    	at android.view.View.layout(View.java:23798)
                                                                                                    	at android.view.ViewGroup.layout(ViewGroup.java:6413)
                                                                                                    	at android.widget.LinearLayout.setChildFrame(LinearLayout.java:1891)
                                                                                                    	at android.widget.LinearLayout.layoutHorizontal(LinearLayout.java:1880)
                                                                                                    	at android.widget.LinearLayout.onLayout(LinearLayout.java:1640)
                                                                                                    	at android.view.View.layout(View.java:23798)
                                                                                                    	at android.view.ViewGroup.layout(ViewGroup.java:6413)
                                                                                                    	at android.widget.RelativeLayout.onLayout(RelativeLayout.java:1103)
                                                                                                    	at android.view.View.layout(View.java:23798)
                                                                                                    	at android.view.ViewGroup.layout(ViewGroup.java:6413)
                                                                                                    	at android.widget.FrameLayout.layoutChildren(FrameLayout.java:332)
                                                                                                    	at android.widget.FrameLayout.onLayout(FrameLayout.java:270)
                                                                                                    	at android.view.View.layout(View.java:23798)
                                                                                                    	at android.view.ViewGroup.layout(ViewGroup.java:6413)
                                                                                                    	at android.widget.FrameLayout.layoutChildren(FrameLayout.java:332)
                                                                                                    	at android.widget.FrameLayout.onLayout(FrameLayout.java:270)
                                                                                                    	at android.view.View.layout(View.java:23798)
                                                                                                    	at android.view.ViewGroup.layout(ViewGroup.java:6413)
                                                                                                    	at android.widget.RelativeLayout.onLayout(RelativeLayout.java:1103)
                                                                                                    	at android.view.View.layout(View.java:23798)
                                                                                                    	at android.view.ViewGroup.layout(ViewGroup.java:6413)
                                                                                                    	at android.widget.FrameLayout.layoutChildren(FrameLayout.java:332)
                                                                                                    	at android.widget.FrameLayout.onLayout(FrameLayout.java:270)
                                                                                                    	at android.view.View.layout(View.java:23798)
                                                                                                    	at android.view.ViewGroup.layout(ViewGroup.java:6413)
                                                                                                    	at android.widget.LinearLayout.setChildFrame(LinearLayout.java:1891)
                                                                                                    	at android.widget.LinearLayout.layoutVertical(LinearLayout.java:1729)
                                                                                                    	at android.widget.LinearLayout.onLayout(LinearLayout.java:1638)
                                                                                                    	at android.view.View.layout(View.java:23798)
                                                                                                    	at android.view.ViewGroup.layout(ViewGroup.java:6413)
                                                                                                    	at android.widget.FrameLayout.layoutChildren(FrameLayout.java:332)
                                                                                                    	at android.widget.FrameLayout.onLayout(FrameLayout.java:270)
                                                                                                    	at android.view.View.layout(View.java:23798)
                                                                                                    	at android.view.ViewGroup.layout(ViewGroup.java:6413)
                                                                                                    	at android.widget.LinearLayout.setChildFrame(LinearLayout.java:1891)
                                                                                                    	at android.widget.LinearLayout.layoutVertical(LinearLayout.java:1729)
                                                                                                    	at android.widget.LinearLayout.onLayout(LinearLayout.java:1638)
                                                                                                    	at android.view.View.layout(View.java:23798)
                                                                                                    	at android.view.ViewGroup.layout(ViewGroup.java:6413)
                                                                                                    	at android.widget.FrameLayout.layoutChildren(FrameLayout.java:332)
                                                                                                    	at android.widget.FrameLayout.onLayout(FrameLayout.java:270)
                                                                                                    	at com.android.internal.policy.DecorView.onLayout(DecorView.java:799)
                                                                                                    	at android.view.View.layout(View.java:23798)
                                                                                                    	at android.view.ViewGroup.layout(ViewGroup.java:6413)
                                                                                                    	at android.view.ViewRootImpl.performLayout(ViewRootImpl.java:4118)
                                                                                                    	at android.view.ViewRootImpl.performTraversals(ViewRootImpl.java:3459)
                                                                                                    	at android.view.ViewRootImpl.doTraversal(ViewRootImpl.java:2371)
                                                                                                    	at android.view.ViewRootImpl$TraversalRunnable.run(ViewRootImpl.java:9297)
                                                                                                    	at android.view.Choreographer$CallbackRecord.run(Choreographer.java:1231)
                                                                                                    	at android.view.Choreographer$CallbackRecord.run(Choreographer.java:1239)
                                                                                                    	at android.view.Choreographer.doCallbacks(Choreographer.java:899)
                                                                                                    	at android.view.Choreographer.doFrame(Choreographer.java:832)
                                                                                                    	at android.view.Choreographer$FrameDisplayEventReceiver.run(Choreographer.java:1214)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:942)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:99)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:201)
                                                                                                    	at android.os.Looper.loop(Looper.java:288)
2025-05-24 15:24:45.776 15884-15884 AndroidRuntime          com.example.aimusicplayer            E  	at android.app.ActivityThread.main(ActivityThread.java:7924) (Ask Gemini)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)
2025-05-24 15:24:45.963   576-1979  InputDispatcher         system_server                        E  But another display has a focused window
                                                                                                      FocusedWindows:
                                                                                                        displayId=8, name='ff3e4b3 com.android.systemui/com.android.systemui.car.distantdisplay.activity.RootTaskViewWallpaperActivity'
                                                                                                        displayId=3, name='678a1bd com.android.systemui/com.android.systemui.car.distantdisplay.activity.DistantDisplayActivity'
                                                                                                        displayId=7, name='7a010fa com.android.systemui/com.android.systemui.car.distantdisplay.activity.NavigationTaskViewWallpaperActivity'
                                                                                                        displayId=2, name='2d2ce0d com.android.car.cluster.osdouble/com.android.car.cluster.osdouble.ClusterOsDoubleActivity'
2025-05-24 15:24:46.079  4386-4461  OpenGLRenderer          com.android.car.carlauncher          E  Unable to match the desired swap behavior.
2025-05-24 15:24:46.126   362-362   ClientCache             surfaceflinger                       E  failed to get buffer, invalid process token
2025-05-24 15:24:46.128   362-362   BpTransact...edListener surfaceflinger                       E  Failed to transact (-32)
2025-05-24 15:24:47.788   576-1633  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-24 15:24:47.788   576-1633  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-24 15:24:47.788   576-1633  TaskPersister           system_server                        E  File error accessing recents directory (directory doesn't exist?).
2025-05-24 15:26:26.692 14175-14205 Finsky                  com.android.vending                  E  [320] okc.i(3): Failed to registerSync with Phenotype for experiment package com.google.android.finsky.regular. (Ask Gemini)
                                                                                                    java.util.concurrent.ExecutionException: com.google.android.libraries.phenotype.client.api.PhenotypeRuntimeException: 29504: 29504: Network error
                                                                                                    	at abkz.r(PG:21)
                                                                                                    	at abkz.get(PG:10)
                                                                                                    	at okc.h(PG:39)
                                                                                                    	at okc.doInBackground(PG:42)
                                                                                                    	at oke.d(PG:4)
                                                                                                    	at man.call(PG:997)
                                                                                                    	at abnn.a(PG:3)
                                                                                                    	at abmr.run(PG:21)
                                                                                                    	at abno.run(PG:5)
                                                                                                    	at abng.run(PG:76)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at dvo.run(PG:38)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
                                                                                                    Caused by: com.google.android.libraries.phenotype.client.api.PhenotypeRuntimeException: 29504: 29504: Network error
                                                                                                    	at xvr.call(PG:475)
                                                                                                    	at abnn.a(PG:3)
                                                                                                    	at abmr.run(PG:21)
                                                                                                    	at abno.run(PG:5)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637) 
                                                                                                    	at dvo.run(PG:38) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
                                                                                                    Caused by: com.google.android.gms.phenotype.core.common.PhenotypeRuntimeException: 29504: Network error
                                                                                                    	at vsy.D(PG:230)
                                                                                                    	at vsy.n(PG:108)
                                                                                                    	at xvr.call(PG:386)
                                                                                                    	at abnn.a(PG:3) 
                                                                                                    	at abmr.run(PG:21) 
                                                                                                    	at abno.run(PG:5) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637) 
                                                                                                    	at dvo.run(PG:38) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
                                                                                                    Caused by: org.apache.http.conn.ConnectTimeoutException: Connect to /142.251.33.74:443 timed out
                                                                                                    	at org.apache.http.conn.scheme.PlainSocketFactory.connectSocket(PlainSocketFactory.java:126)
                                                                                                    	at org.apache.http.impl.conn.DefaultClientConnectionOperator.openConnection(DefaultClientConnectionOperator.java:149)
                                                                                                    	at org.apache.http.impl.conn.AbstractPoolEntry.open(AbstractPoolEntry.java:170)
                                                                                                    	at org.apache.http.impl.conn.AbstractPooledConnAdapter.open(AbstractPooledConnAdapter.java:124)
                                                                                                    	at org.apache.http.impl.client.DefaultRequestDirector.execute(DefaultRequestDirector.java:366)
                                                                                                    	at org.apache.http.impl.client.AbstractHttpClient.execute(AbstractHttpClient.java:560)
                                                                                                    	at org.apache.http.impl.client.AbstractHttpClient.execute(AbstractHttpClient.java:492)
                                                                                                    	at org.apache.http.impl.client.AbstractHttpClient.execute(AbstractHttpClient.java:470)
                                                                                                    	at zba.execute(PG:5)
                                                                                                    	at abda.d(PG:7)
                                                                                                    	at zbe.b(PG:82)
                                                                                                    	at zbe.execute(PG:23)
                                                                                                    	at zbe.execute(PG:13)
                                                                                                    	at vtc.a(PG:230)
                                                                                                    	at vtw.g(PG:1)
                                                                                                    	at vtc.b(PG:1)
                                                                                                    	at vsy.D(PG:18)
                                                                                                    	at vsy.n(PG:108) 
                                                                                                    	at xvr.call(PG:386) 
                                                                                                    	at abnn.a(PG:3) 
                                                                                                    	at abmr.run(PG:21) 
                                                                                                    	at abno.run(PG:5) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637) 
                                                                                                    	at dvo.run(PG:38) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
2025-05-24 15:26:26.695 14175-14205 Finsky                  com.android.vending                  E  [320] kzf.a(19): HC: Failed to preload experiments flags: java.util.concurrent.ExecutionException: com.google.android.libraries.phenotype.client.api.PhenotypeRuntimeException: 29504: 29504: Network error
2025-05-24 15:26:27.195 14175-14222 Finsky                  com.android.vending                  E  [338] iuw.a(52): Unexpected android-id = 0
2025-05-24 15:26:27.195 14175-14222 Finsky                  com.android.vending                  E  [338] rcz.WA(261): [PLUS] Failed to trigger NOW_EXCLUSIVE urgency sync (Ask Gemini)
                                                                                                    java.lang.IllegalStateException: Cannot sync with 0 Android ID
                                                                                                    	at suw.a(PG:71)
                                                                                                    	at rak.a(PG:144)
                                                                                                    	at ablb.d(PG:3)
                                                                                                    	at abld.run(PG:42)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at kiq.run(PG:324)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-24 15:26:27.196 14175-14222 Finsky                  com.android.vending                  E  [338] gmr.WA(29): [DeviceConfig] failed to update device attribute payloads (Ask Gemini)
                                                                                                    java.lang.IllegalStateException: Cannot sync with 0 Android ID
                                                                                                    	at suw.a(PG:71)
                                                                                                    	at rak.a(PG:144)
                                                                                                    	at ablb.d(PG:3)
                                                                                                    	at abld.run(PG:42)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at kiq.run(PG:324)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-24 15:26:27.217 14175-14226 Finsky                  com.android.vending                  E  [341] iuw.a(52): Unexpected android-id = 0
2025-05-24 15:26:27.570 14257-14310 Finsky                  com.android.vending                  E  [318] okc.i(3): Failed to registerSync with Phenotype for experiment package com.google.android.finsky.regular. (Ask Gemini)
                                                                                                    java.util.concurrent.ExecutionException: com.google.android.libraries.phenotype.client.api.PhenotypeRuntimeException: 29504: 29504: Network error
                                                                                                    	at abkz.r(PG:21)
                                                                                                    	at abkz.get(PG:10)
                                                                                                    	at okc.h(PG:39)
                                                                                                    	at okc.doInBackground(PG:42)
                                                                                                    	at oke.d(PG:4)
                                                                                                    	at man.call(PG:997)
                                                                                                    	at abnn.a(PG:3)
                                                                                                    	at abmr.run(PG:21)
                                                                                                    	at abno.run(PG:5)
                                                                                                    	at abng.run(PG:76)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at dvo.run(PG:38)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
                                                                                                    Caused by: com.google.android.libraries.phenotype.client.api.PhenotypeRuntimeException: 29504: 29504: Network error
                                                                                                    	at xvr.call(PG:475)
                                                                                                    	at abnn.a(PG:3)
                                                                                                    	at abmr.run(PG:21)
                                                                                                    	at abno.run(PG:5)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637) 
                                                                                                    	at dvo.run(PG:38) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
                                                                                                    Caused by: com.google.android.gms.phenotype.core.common.PhenotypeRuntimeException: 29504: Network error
                                                                                                    	at vsy.D(PG:230)
                                                                                                    	at vsy.n(PG:108)
                                                                                                    	at xvr.call(PG:386)
                                                                                                    	at abnn.a(PG:3) 
                                                                                                    	at abmr.run(PG:21) 
                                                                                                    	at abno.run(PG:5) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637) 
                                                                                                    	at dvo.run(PG:38) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
                                                                                                    Caused by: org.apache.http.conn.ConnectTimeoutException: Connect to /142.251.33.74:443 timed out
                                                                                                    	at org.apache.http.conn.scheme.PlainSocketFactory.connectSocket(PlainSocketFactory.java:126)
                                                                                                    	at org.apache.http.impl.conn.DefaultClientConnectionOperator.openConnection(DefaultClientConnectionOperator.java:149)
                                                                                                    	at org.apache.http.impl.conn.AbstractPoolEntry.open(AbstractPoolEntry.java:170)
                                                                                                    	at org.apache.http.impl.conn.AbstractPooledConnAdapter.open(AbstractPooledConnAdapter.java:124)
                                                                                                    	at org.apache.http.impl.client.DefaultRequestDirector.execute(DefaultRequestDirector.java:366)
                                                                                                    	at org.apache.http.impl.client.AbstractHttpClient.execute(AbstractHttpClient.java:560)
                                                                                                    	at org.apache.http.impl.client.AbstractHttpClient.execute(AbstractHttpClient.java:492)
                                                                                                    	at org.apache.http.impl.client.AbstractHttpClient.execute(AbstractHttpClient.java:470)
                                                                                                    	at zba.execute(PG:5)
                                                                                                    	at abda.d(PG:7)
                                                                                                    	at zbe.b(PG:82)
                                                                                                    	at zbe.execute(PG:23)
                                                                                                    	at zbe.execute(PG:13)
                                                                                                    	at vtc.a(PG:230)
                                                                                                    	at vtw.g(PG:1)
                                                                                                    	at vtc.b(PG:1)
                                                                                                    	at vsy.D(PG:18)
                                                                                                    	at vsy.n(PG:108) 
                                                                                                    	at xvr.call(PG:386) 
                                                                                                    	at abnn.a(PG:3) 
                                                                                                    	at abmr.run(PG:21) 
                                                                                                    	at abno.run(PG:5) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637) 
                                                                                                    	at dvo.run(PG:38) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
2025-05-24 15:26:27.575 14257-14310 Finsky                  com.android.vending                  E  [318] kzf.a(19): HC: Failed to preload experiments flags: java.util.concurrent.ExecutionException: com.google.android.libraries.phenotype.client.api.PhenotypeRuntimeException: 29504: 29504: Network error
2025-05-24 15:26:27.876 14257-14344 Finsky                  com.android.vending                  E  [332] iuw.a(52): Unexpected android-id = 0
2025-05-24 15:26:27.876 14257-14344 Finsky                  com.android.vending                  E  [332] rcz.WA(261): [PLUS] Failed to trigger NOW_EXCLUSIVE urgency sync (Ask Gemini)
                                                                                                    java.lang.IllegalStateException: Cannot sync with 0 Android ID
                                                                                                    	at suw.a(PG:71)
                                                                                                    	at rak.a(PG:144)
                                                                                                    	at ablb.d(PG:3)
                                                                                                    	at abld.run(PG:42)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at kiq.run(PG:324)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-24 15:26:27.877 14257-14344 Finsky                  com.android.vending                  E  [332] gmr.WA(29): [DeviceConfig] failed to update device attribute payloads (Ask Gemini)
                                                                                                    java.lang.IllegalStateException: Cannot sync with 0 Android ID
                                                                                                    	at suw.a(PG:71)
                                                                                                    	at rak.a(PG:144)
                                                                                                    	at ablb.d(PG:3)
                                                                                                    	at abld.run(PG:42)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at kiq.run(PG:324)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-24 15:26:27.894 14257-14355 Finsky                  com.android.vending                  E  [341] iuw.a(52): Unexpected android-id = 0
2025-05-24 15:26:31.837   346-346   android.ha...rvice-mock <EMAIL>  E  Failed to getEnergyData
2025-05-24 15:26:35.608 14175-14226 Finsky                  com.android.vending                  E  [341] iuw.a(52): Unexpected android-id = 0
2025-05-24 15:26:36.240 14257-14355 Finsky                  com.android.vending                  E  [341] iuw.a(52): Unexpected android-id = 0